'use client';

import React from 'react';
import Link from 'next/link';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Stethoscope, 
  Heart, 
  Brain, 
  ArrowRight, 
  CheckCircle,
  Zap,
  Shield,
  Smartphone
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface SolutionCardProps {
  icon: React.ReactNode;
  title: string;
  description: string;
  features: string[];
  badge?: string;
  href: string;
  className?: string;
}

function SolutionCard({ icon, title, description, features, badge, href, className }: SolutionCardProps) {
  return (
    <Card className={cn(
      "group relative overflow-hidden transition-all duration-300 hover:shadow-lg hover:-translate-y-1 border-border/50 hover:border-primary/20 h-full flex flex-col",
      className
    )}>
      <CardHeader className="pb-3 sm:pb-4 flex-shrink-0">
        <div className="flex items-start justify-between">
          <div className="flex items-start space-x-2 sm:space-x-3 min-w-0 flex-1">
            <div className="w-10 h-10 sm:w-12 sm:h-12 bg-primary/10 rounded-xl flex items-center justify-center group-hover:bg-primary/20 transition-colors flex-shrink-0">
              {icon}
            </div>
            <div className="min-w-0 flex-1">
              <CardTitle className="text-lg sm:text-xl font-semibold group-hover:text-primary transition-colors leading-tight">
                {title}
              </CardTitle>
              {badge && (
                <Badge variant="secondary" className="mt-1 text-xs px-2 py-0.5">
                  {badge}
                </Badge>
              )}
            </div>
          </div>
        </div>
        <CardDescription className="text-sm sm:text-base text-muted-foreground mt-2 sm:mt-3 leading-relaxed">
          {description}
        </CardDescription>
      </CardHeader>

      <CardContent className="space-y-3 sm:space-y-4 flex-1 flex flex-col">
        <ul className="space-y-1.5 sm:space-y-2 flex-1">
          {features.map((feature, index) => (
            <li key={index} className="flex items-start space-x-2 text-xs sm:text-sm">
              <CheckCircle className="w-3 h-3 sm:w-4 sm:h-4 text-accent flex-shrink-0 mt-0.5" />
              <span className="text-muted-foreground leading-relaxed">{feature}</span>
            </li>
          ))}
        </ul>

        <Button
          variant="outline"
          className="w-full group-hover:bg-primary group-hover:text-primary-foreground transition-colors mt-auto min-h-[44px]"
          asChild
        >
          <Link href={href}>
            Learn More
            <ArrowRight className="w-3 h-3 sm:w-4 sm:h-4 ml-2 group-hover:translate-x-1 transition-transform" />
          </Link>
        </Button>
      </CardContent>

      {/* Hover Effect Background */}
      <div className="absolute inset-0 bg-gradient-to-br from-primary/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none" />
    </Card>
  );
}

export function SolutionsSection() {
  const solutions = [
    {
      icon: <Stethoscope className="w-6 h-6 text-primary" />,
      title: "medPower® Telemonitoring",
      description: "Zertifizierte Telemonitoring-Plattform für die digitale Betreuung chronisch erkrankter Patienten mit Echtzeit-Überwachung.",
      features: [
        "Vitaldatenübertragung (Blutdruck, Gewicht, EKG)",
        "Echtzeit-Warnsystem und Alarme",
        "Automatisierte Prozesse",
        "GBA-konforme Datenhaltung"
      ],
      badge: "Beliebteste Lösung",
      href: "#medpower"
    },
    {
      icon: <Heart className="w-6 h-6 text-primary" />,
      title: "Patientenfragebögen (PRM)",
      description: "Digitale Patientenfragebögen zur systematischen Erfassung von Gesundheitsdaten und Symptomen.",
      features: [
        "Individualisierte Fragebögen",
        "Automatische Auswertung",
        "Trend-Analyse",
        "Integration in Behandlungsplan"
      ],
      href: "#patient-surveys"
    },
    {
      icon: <Brain className="w-6 h-6 text-primary" />,
      title: "Medikationsverwaltung",
      description: "Intelligente Medikationsverwaltung mit Erinnerungen und Compliance-Überwachung für bessere Therapietreue.",
      features: [
        "Medikationsplan-Verwaltung",
        "Einnahme-Erinnerungen",
        "Compliance-Monitoring",
        "Nebenwirkungen-Tracking"
      ],
      badge: "KI-gestützt",
      href: "#medication"
    }
  ];

  return (
    <section id="solutions" className="py-12 sm:py-16 lg:py-20 xl:py-24 bg-muted/30">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center max-w-4xl mx-auto mb-12 sm:mb-16">
          <Badge variant="secondary" className="mb-3 sm:mb-4 px-3 py-1.5 text-xs sm:text-sm">
            Unsere Services
          </Badge>
          <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-foreground mb-4 sm:mb-6 leading-tight">
            Komplette Telemedizin-Lösungen für die digitale Patientenversorgung
          </h2>
          <p className="text-base sm:text-lg text-muted-foreground leading-relaxed">
            Von der Vitaldatenübertragung bis zur intelligenten Medikationsverwaltung -
            unsere integrierten Lösungen transformieren die Patientenbetreuung und verbessern die Behandlungsergebnisse.
          </p>
        </div>

        {/* Solutions Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8 mb-12 sm:mb-16">
          {solutions.map((solution, index) => (
            <SolutionCard
              key={solution.title}
              {...solution}
              className=""
            />
          ))}
        </div>

        {/* Key Benefits */}
        <div className="bg-card rounded-xl sm:rounded-2xl p-6 sm:p-8 border border-border">
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 sm:gap-8">
            <div className="text-center">
              <div className="w-12 h-12 sm:w-16 sm:h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-3 sm:mb-4">
                <Shield className="w-6 h-6 sm:w-8 sm:h-8 text-primary" />
              </div>
              <h3 className="font-semibold text-foreground mb-2 text-sm sm:text-base">GBA-konform</h3>
              <p className="text-xs sm:text-sm text-muted-foreground leading-relaxed">
                Zertifizierte Telemedizin-Plattform nach deutschen Gesundheitsstandards und DSGVO-konform
              </p>
            </div>

            <div className="text-center">
              <div className="w-12 h-12 sm:w-16 sm:h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-3 sm:mb-4">
                <Zap className="w-6 h-6 sm:w-8 sm:h-8 text-primary" />
              </div>
              <h3 className="font-semibold text-foreground mb-2 text-sm sm:text-base">Cloud-basierte Technologie</h3>
              <p className="text-xs sm:text-sm text-muted-foreground leading-relaxed">
                Moderne cloudbasierte Architektur für nahtlose Integration und Skalierbarkeit
              </p>
            </div>

            <div className="text-center">
              <div className="w-12 h-12 sm:w-16 sm:h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-3 sm:mb-4">
                <Smartphone className="w-6 h-6 sm:w-8 sm:h-8 text-primary" />
              </div>
              <h3 className="font-semibold text-foreground mb-2 text-sm sm:text-base">Web- & mobilfähig</h3>
              <p className="text-xs sm:text-sm text-muted-foreground leading-relaxed">
                Responsive Benutzeroberfläche für alle Geräte und Bildschirmgrößen optimiert
              </p>
            </div>

            <div className="text-center">
              <div className="w-12 h-12 sm:w-16 sm:h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-3 sm:mb-4">
                <Heart className="w-6 h-6 sm:w-8 sm:h-8 text-primary" />
              </div>
              <h3 className="font-semibold text-foreground mb-2 text-sm sm:text-base">Patientenzentriert</h3>
              <p className="text-xs sm:text-sm text-muted-foreground leading-relaxed">
                Entwickelt mit Fokus auf Patientensicherheit und Versorgungsqualität
              </p>
            </div>
          </div>
        </div>

        {/* CTA Section */}
        <div className="text-center mt-12 sm:mt-16">
          <h3 className="text-xl sm:text-2xl font-semibold text-foreground mb-3 sm:mb-4 leading-tight">
            Bereit für die digitale Transformation Ihrer Patientenversorgung?
          </h3>
          <p className="text-sm sm:text-base text-muted-foreground mb-6 sm:mb-8 max-w-2xl mx-auto leading-relaxed">
            Schließen Sie sich hunderten von Arztpraxen und Gesundheitszentren an, die AITELMED für ihre Telemedizin-Bedürfnisse vertrauen.
          </p>
          <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center max-w-md sm:max-w-none mx-auto">
            <Button size="lg" className="min-h-[48px]" asChild>
              <Link href="/request-demo">
                Kostenlose Demo anfordern
                <ArrowRight className="w-4 h-4 ml-2" />
              </Link>
            </Button>
            <Button variant="outline" size="lg" className="min-h-[48px]" asChild>
              <Link href="/configurator">
                Plattform testen
              </Link>
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
}
