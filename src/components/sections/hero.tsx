'use client';

import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { useTranslations, useLocale } from 'next-intl';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { CheckCircle, Award, Users, Clock } from 'lucide-react';
import { cn } from '@/lib/utils';

interface TrustIndicatorProps {
  icon: React.ReactNode;
  value: string;
  label: string;
}

function TrustIndicator({ icon, value, label }: TrustIndicatorProps) {
  return (
    <div className="flex items-center space-x-2 sm:space-x-3 text-center sm:text-left">
      <div className="flex-shrink-0 w-8 h-8 sm:w-10 sm:h-10 bg-accent/10 rounded-full flex items-center justify-center">
        {icon}
      </div>
      <div className="min-w-0 flex-1">
        <div className="text-lg sm:text-xl lg:text-2xl font-bold text-foreground">{value}</div>
        <div className="text-xs sm:text-sm text-muted-foreground leading-tight">{label}</div>
      </div>
    </div>
  );
}

export function HeroSection() {
  const t = useTranslations('hero');
  const tCta = useTranslations('cta');
  const locale = useLocale();

  return (
    <section className="relative overflow-hidden bg-gradient-to-br from-background via-background to-primary/5">
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-grid-pattern opacity-5" />

      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-12 sm:py-16 lg:py-20 xl:py-24">
        <div className="grid lg:grid-cols-2 gap-8 sm:gap-10 lg:gap-12 xl:gap-16 items-center">
          {/* Content Column */}
          <div className="space-y-6 sm:space-y-8 order-2 lg:order-1">
            {/* Certification Badge */}
            <div className="flex justify-center lg:justify-start">
              <Badge variant="secondary" className="px-3 py-1.5 sm:px-4 sm:py-2 text-xs sm:text-sm font-medium bg-accent/10 text-accent border-accent/20">
                <Award className="w-3 h-3 sm:w-4 sm:h-4 mr-1.5 sm:mr-2" />
                {t('badge')}
              </Badge>
            </div>

            {/* Main Headline */}
            <div className="space-y-3 sm:space-y-4 text-center lg:text-left">
              <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-5xl xl:text-6xl font-bold tracking-tight text-foreground leading-tight">
                <span className="text-primary">AITELMED</span>
                <br />
                {t('title')}
              </h1>

              <p className="text-lg sm:text-xl md:text-2xl text-muted-foreground font-medium leading-relaxed">
                <span className="text-primary font-semibold">{t('subtitle')}</span>
              </p>

              <p className="text-base sm:text-lg text-muted-foreground max-w-2xl mx-auto lg:mx-0 leading-relaxed">
                {t('description')}
              </p>
            </div>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center lg:justify-start">
              <Button size="lg" className="text-sm sm:text-base px-6 sm:px-8 py-4 sm:py-6 h-auto min-h-[48px] font-medium" asChild>
                <Link href={`/${locale}/schedule-consultation`}>
                  <CheckCircle className="w-4 h-4 sm:w-5 sm:h-5 mr-2" />
                  {tCta('scheduleConsultation')}
                </Link>
              </Button>

              <Button variant="outline" size="lg" className="text-sm sm:text-base px-6 sm:px-8 py-4 sm:py-6 h-auto min-h-[48px] font-medium" asChild>
                <Link href={`/${locale}/schedule-consultation`}>
                  {tCta('learnMore')}
                </Link>
              </Button>
            </div>

            {/* Trust Indicators */}
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 sm:gap-6 pt-6 sm:pt-8 border-t border-border">
              <TrustIndicator
                icon={<Award className="w-4 h-4 sm:w-5 sm:h-5 text-accent" />}
                value={t('trustIndicators.gba')}
                label={t('trustIndicators.gbaLabel')}
              />
              <TrustIndicator
                icon={<Users className="w-4 h-4 sm:w-5 sm:h-5 text-accent" />}
                value={t('trustIndicators.patients')}
                label={t('trustIndicators.patientsLabel')}
              />
              <TrustIndicator
                icon={<Clock className="w-4 h-4 sm:w-5 sm:h-5 text-accent" />}
                value={t('trustIndicators.monitoring')}
                label={t('trustIndicators.monitoringLabel')}
              />
            </div>
          </div>

          {/* Visual Column */}
          <div className="relative order-1 lg:order-2">
            {/* Hero Image Placeholder */}
            <div className="relative aspect-[4/3] sm:aspect-[5/4] lg:aspect-[4/3] rounded-xl sm:rounded-2xl overflow-hidden bg-gradient-to-br from-primary/10 to-accent/10 border border-border">
              <div className="absolute inset-0 flex items-center justify-center p-4">
                <div className="text-center space-y-3 sm:space-y-4">
                  <div className="w-16 h-16 sm:w-20 sm:h-20 lg:w-24 lg:h-24 bg-primary/20 rounded-full flex items-center justify-center mx-auto">
                    <div className="w-8 h-8 sm:w-10 sm:h-10 lg:w-12 lg:h-12 bg-primary rounded-full flex items-center justify-center">
                      <span className="text-primary-foreground font-bold text-sm sm:text-lg lg:text-xl">NC</span>
                    </div>
                  </div>
                  <div className="space-y-1 sm:space-y-2">
                    <h3 className="text-lg sm:text-xl font-semibold text-foreground">{t('visual.title')}</h3>
                    <p className="text-sm sm:text-base text-muted-foreground">{t('visual.subtitle')}</p>
                  </div>
                </div>
              </div>

              {/* Floating Elements */}
              <div className="absolute top-2 right-2 sm:top-4 sm:right-4">
                <Card className="p-2 sm:p-3 shadow-lg">
                  <CardContent className="p-0 flex items-center space-x-1.5 sm:space-x-2">
                    <div className="w-2 h-2 sm:w-3 sm:h-3 bg-accent rounded-full animate-pulse" />
                    <span className="text-xs font-medium">{t('visual.statusActive')}</span>
                  </CardContent>
                </Card>
              </div>

              <div className="absolute bottom-2 left-2 sm:bottom-4 sm:left-4">
                <Card className="p-2 sm:p-3 shadow-lg">
                  <CardContent className="p-0 flex items-center space-x-1.5 sm:space-x-2">
                    <CheckCircle className="w-3 h-3 sm:w-4 sm:h-4 text-accent" />
                    <span className="text-xs font-medium">{t('visual.certified')}</span>
                  </CardContent>
                </Card>
              </div>
            </div>

            {/* Background Decorations */}
            <div className="absolute -top-2 -right-2 sm:-top-4 sm:-right-4 w-16 h-16 sm:w-24 sm:h-24 bg-primary/10 rounded-full blur-xl" />
            <div className="absolute -bottom-2 -left-2 sm:-bottom-4 sm:-left-4 w-20 h-20 sm:w-32 sm:h-32 bg-accent/10 rounded-full blur-xl" />
          </div>
        </div>

        {/* Additional Trust Indicators */}
        <div className="mt-12 sm:mt-16 pt-6 sm:pt-8 border-t border-border">
          <div className="text-center mb-6 sm:mb-8">
            <p className="text-xs sm:text-sm text-muted-foreground uppercase tracking-wide font-medium">
              {t('clientsTitle')}
            </p>
          </div>

          <div className="grid grid-cols-2 sm:grid-cols-4 gap-4 sm:gap-6 lg:gap-8 items-center opacity-60">
            {/* Placeholder for client logos */}
            {Array.from({ length: 4 }).map((_, index) => (
              <div
                key={index}
                className="h-10 sm:h-12 bg-muted rounded-lg flex items-center justify-center px-2"
              >
                <span className="text-muted-foreground text-xs sm:text-sm font-medium text-center">
                  Praxis Partner {index + 1}
                </span>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}
